{"ast": null, "code": "var _jsxFileName = \"D:\\\\WORK\\\\Test\\\\src\\\\index.js\";\nimport React from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport App from \"./App\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 6,\n  columnNumber: 13\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "fileName", "_jsxFileName", "lineNumber", "columnNumber"], "sources": ["D:/WORK/Test/src/index.js"], "sourcesContent": ["import React from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport App from \"./App\";\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\r\nroot.render(<App />);"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,GAAG,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,MAAMC,IAAI,GAAGJ,QAAQ,CAACK,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cAACL,OAAA,CAACF,GAAG;EAAAQ,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}