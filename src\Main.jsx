import React from "react";

const Main = () => (
  <div className="w-[1259px] inline-flex flex-col justify-start items-start gap-6">
    <div className="self-stretch inline-flex justify-start items-start gap-48">
      <div className="w-[673px] flex justify-start items-end gap-2.5">
        <div className="w-16 h-16 relative overflow-hidden">
          <div className="w-16 h-16 left-0 top-0 absolute bg-white rounded-full border border-indigo-400" />
          <img className="w-16 h-16 left-0 top-0 absolute rounded-full" src="https://placehold.co/69x69" />
        </div>
        <div className="flex-1 self-stretch inline-flex flex-col justify-center items-start gap-1">
          <div className="self-stretch justify-center text-black text-3xl font-semibold font-['Plus_Jakarta_Sans'] leading-[48px]">Lorem ipsum dolor sit amet</div>
          <div className="self-stretch justify-center text-black text-xs font-normal font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna. </div>
        </div>
      </div>
      <div className="w-96 h-16 inline-flex flex-col justify-end items-end gap-2.5">
        <div className="px-3 py-2 rounded-lg outline outline-1 outline-offset-[-1px] outline-gray-500 inline-flex justify-center items-center gap-2">
          <div data-size="48" className="w-4 h-4 relative overflow-hidden">
            <div className="w-3 h-3 left-[2px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
          </div>
        </div>
      </div>
    </div>
    <div className="self-stretch inline-flex justify-center items-start gap-4">
      <div className="w-[847px] inline-flex flex-col justify-start items-start">
        <div className="self-stretch flex flex-col justify-start items-start gap-6">
          <div className="self-stretch inline-flex justify-start items-center gap-3">
            <div className="w-52 h-16 px-3.5 py-3 bg-white rounded-2xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex justify-start items-center gap-2.5 overflow-hidden">
              <div className="flex-1 inline-flex flex-col justify-center items-center">
                <div className="self-stretch justify-center text-stone-900 text-3xl font-semibold font-['Plus_Jakarta_Sans'] leading-9">3</div>
                <div className="self-stretch justify-center text-gray-800 text-[10px] font-light font-['Inter'] leading-3">Total Analysis</div>
              </div>
              <div className="w-12 h-12 bg-indigo-100 rounded-full" />
            </div>
            <div className="w-52 h-16 px-3.5 py-2 bg-white rounded-2xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex justify-start items-center gap-2.5 overflow-hidden">
              <div className="flex-1 inline-flex flex-col justify-center items-center">
                <div className="self-stretch justify-center text-stone-900 text-3xl font-semibold font-['Plus_Jakarta_Sans'] leading-9">4</div>
                <div className="self-stretch justify-center text-gray-800 text-[10px] font-light font-['Inter'] leading-3">Completed</div>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full" />
            </div>
            <div className="w-52 h-16 px-3.5 py-3 bg-white rounded-2xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex justify-start items-center gap-2.5 overflow-hidden">
              <div className="flex-1 inline-flex flex-col justify-center items-center">
                <div className="self-stretch justify-center text-stone-900 text-3xl font-semibold font-['Plus_Jakarta_Sans'] leading-9">6</div>
                <div className="self-stretch justify-center text-gray-800 text-[10px] font-light font-['Inter'] leading-3">Processing</div>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full" />
            </div>
            <div className="w-52 h-16 px-3.5 py-3 bg-white rounded-2xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex justify-start items-center gap-2.5 overflow-hidden">
              <div className="flex-1 inline-flex flex-col justify-center items-center">
                <div className="self-stretch justify-center text-stone-900 text-3xl font-semibold font-['Plus_Jakarta_Sans'] leading-9">2</div>
                <div className="self-stretch justify-center text-gray-800 text-[10px] font-light font-['Inter'] leading-3">Token Balance</div>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-full" />
            </div>
          </div>
          <div className="self-stretch rounded shadow-[0px_4px_4px_0px_rgba(0,0,0,0.10)] flex flex-col justify-start items-start">
            <div className="self-stretch p-4 bg-white rounded-tl-xl rounded-tr-xl outline outline-1 outline-offset-[-1px] outline-gray-200 inline-flex justify-between items-center">
              <div className="flex-1 self-stretch inline-flex flex-col justify-start items-start">
                <div className="self-stretch justify-center text-black text-xl font-semibold font-['Plus_Jakarta_Sans'] leading-loose">Assessment History</div>
                <div className="self-stretch justify-center text-gray-500 text-xs font-normal font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna. </div>
              </div>
              <div className="px-3 py-2 bg-indigo-500 rounded-lg flex justify-center items-center gap-2">
                <div className="w-4 h-4 relative overflow-hidden">
                  <div className="w-3 h-3 left-[2px] top-[2px] absolute bg-white" />
                </div>
                <div className="justify-start text-white text-xs font-bold font-['Roboto']">New Assessment</div>
              </div>
            </div>
            <div className="self-stretch flex flex-col justify-start items-start">
              <div className="self-stretch p-4 bg-white border-l border-r border-b border-gray-200 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-medium font-['Roboto']">Nomor</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-1">
                  <div className="flex-1 justify-start text-black text-sm font-bold font-['Roboto']">Nama</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-bold font-['Roboto']">Tipe Ujian</div>
                  <div className="w-4 h-4 relative overflow-hidden">
                    <div className="w-2.5 h-3 left-[3.48px] top-[1.67px] absolute bg-neutral-400" />
                  </div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-bold font-['Roboto']">Tanggal Ujian</div>
                  <div className="w-4 h-4 relative overflow-hidden">
                    <div className="w-2.5 h-3 left-[3.48px] top-[1.67px] absolute bg-neutral-400" />
                  </div>
                </div>
                <div className="flex-1 flex justify-start items-start gap-2">
                  <div className="flex-1 text-center justify-start text-black text-sm font-bold font-['Roboto']">Action</div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">1</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Matematika</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">12 Juni 2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">2</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Bahasa Inggris</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">12 Juni 2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">3</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Bahasa Indonesia</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">22 Juni 2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">4</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Biologi</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Essay</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">22 Juni 2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">6</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Bahasa Inggris</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">12 Juni 2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">7</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Kimia</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Essay</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">16 Juni  2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">8</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Fisika</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">18 Juni  2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-b border-zinc-100 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">9</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Fisika</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">18 Juni  2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="self-stretch px-4 py-4 bg-white border-l border-r border-gray-200 inline-flex justify-center items-center gap-4">
                <div className="w-20 flex justify-start items-start gap-1">
                  <div className="flex-1 text-center justify-start text-black text-sm font-normal font-['Roboto']">10</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">Fisika</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">PG</div>
                </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 justify-start text-black text-sm font-normal font-['Roboto']">18 Juni  2024</div>
                </div>
                <div className="flex-1 flex justify-center items-center gap-3">
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-format="Stroke" data-weight="Regular" className="w-6 h-6 relative">
                      <div className="w-6 h-6 left-0 top-0 absolute" />
                      <div className="w-1.5 h-1.5 left-[14.25px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-2 h-2 left-[12.75px] top-[3.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                      <div className="w-3.5 h-3.5 left-[3.75px] top-[6.75px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                  <div className="h-6 flex justify-start items-center gap-3.5">
                    <div data-size="48" className="w-6 h-6 relative overflow-hidden">
                      <div className="w-4 h-5 left-[3px] top-[2px] absolute outline outline-[1.50px] outline-offset-[-0.75px] outline-gray-500" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="self-stretch p-4 bg-white rounded-bl-xl rounded-br-xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex flex-col justify-center items-end">
              <div className="self-stretch inline-flex justify-between items-center">
                <div className="flex justify-center items-center gap-3">
                  <div className="justify-start text-black text-xs font-medium font-['Roboto']">Show</div>
                  <div className="px-2 py-2 bg-zinc-100 rounded-lg flex justify-start items-center gap-1">
                    <div className="justify-start text-black text-xs font-medium font-['Roboto']">10</div>
                    <div className="w-2 h-2 relative overflow-hidden">
                      <div className="w-1.5 h-1 left-[1.10px] top-[2px] absolute bg-neutral-400" />
                    </div>
                  </div>
                  <div className="justify-start text-black text-xs font-medium font-['Roboto']">Data</div>
                </div>
                <div className="flex justify-start items-center gap-3">
                  <div className="px-2 py-2 bg-indigo-500 rounded-lg flex justify-start items-center gap-1">
                    <div className="w-3 h-3.5 text-center justify-start text-white text-xs font-medium font-['Roboto']">1</div>
                  </div>
                  <div className="px-2 py-2 bg-zinc-100 rounded-lg flex justify-start items-center gap-1">
                    <div className="w-3 h-3.5 text-center justify-start text-black text-xs font-medium font-['Roboto']">2</div>
                  </div>
                  <div className="px-2 py-2 bg-zinc-100 rounded-lg flex justify-start items-center gap-1">
                    <div className="w-3 h-3.5 text-center justify-start text-black text-xs font-medium font-['Roboto']">3</div>
                  </div>
                </div>
                <div className="p-2 rounded-lg flex justify-center items-center gap-2" />
              </div>
            </div>
          </div>
        </div>
        <div className="w-96 inline-flex flex-col justify-start items-end gap-4">
          <div className="self-stretch px-4 py-4 bg-white rounded-xl outline outline-1 outline-offset-[-1px] outline-gray-200 inline-flex justify-start items-center gap-2.5">
            <div className="w-96 inline-flex flex-col justify-start items-center gap-3">
              <div className="w-80 flex flex-col justify-start items-center gap-[5px]">
                <div className="w-40 h-40 relative overflow-hidden">
                  <div className="w-40 h-40 left-0 top-[0.14px] absolute bg-indigo-200 rounded-full" />
                  <div className="w-48 h-32 left-[102.63px] top-[193.27px] absolute origin-top-left rotate-[-135deg] bg-indigo-500 rounded-full" />
                  <img className="w-32 h-32 left-[17px] top-[17px] absolute rounded-full" src="https://placehold.co/126x126" />
                </div>
                <div className="self-stretch h-16 flex flex-col justify-center items-center">
                  <div className="self-stretch text-center justify-center text-black text-2xl font-semibold font-['Plus_Jakarta_Sans'] leading-10">Lorem ipsum dolor sit amet</div>
                  <div className="self-stretch text-center justify-center text-gray-500 text-xs font-normal font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
                </div>
              </div>
              <div className="self-stretch p-3 rounded-2xl flex flex-col justify-center items-center gap-2.5">
                <div className="inline-flex justify-start items-center gap-2.5">
                  <div className="w-14 h-32 relative">
                    <div className="w-14 h-32 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
                    <div className="w-14 h-24 left-0 top-[34px] absolute bg-indigo-300 rounded-xl" />
                  </div>
                  <div className="w-14 h-32 relative">
                    <div className="w-14 h-32 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
                    <div className="w-14 h-32 left-0 top-[14px] absolute bg-indigo-500 rounded-xl" />
                  </div>
                  <div className="w-14 h-32 relative">
                    <div className="w-14 h-32 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
                    <div className="w-14 h-9 left-0 top-[99px] absolute bg-indigo-300 rounded-xl" />
                  </div>
                  <div className="w-14 h-32 relative">
                    <div className="w-14 h-32 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
                    <div className="w-14 h-20 left-0 top-[58px] absolute bg-indigo-500 rounded-xl" />
                  </div>
                  <div className="w-14 h-32 relative">
                    <div className="w-14 h-32 left-0 top-0 absolute bg-zinc-300 rounded-xl" />
                    <div className="w-14 h-10 left-0 top-[96px] absolute bg-indigo-300 rounded-xl" />
                  </div>
                </div>
                <div className="self-stretch px-6 inline-flex justify-between items-start">
                  <div className="w-6 h-6 bg-purple-100 rounded-full" />
                  <div className="w-6 h-6 bg-purple-100 rounded-full" />
                  <div className="w-6 h-6 bg-purple-100 rounded-full" />
                  <div className="w-6 h-6 bg-purple-100 rounded-full" />
                  <div className="w-6 h-6 bg-purple-100 rounded-full" />
                </div>
              </div>
            </div>
          </div>
          <div className="self-stretch h-80 bg-white rounded-xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex flex-col justify-start items-start">
            <div className="self-stretch px-3 py-3 bg-white rounded-tl-xl rounded-tr-xl outline outline-1 outline-offset-[-1px] outline-gray-200 flex flex-col justify-center items-start">
              <div className="self-stretch justify-center text-black text-lg font-semibold font-['Plus_Jakarta_Sans'] leading-7">Lorem ipsum dolor sit amet</div>
              <div className="self-stretch justify-center text-gray-500 text-xs font-normal font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
            </div>
            <div className="self-stretch px-4 py-4 rounded-xl outline outline-1 outline-offset-[-1px] flex flex-col justify-start items-start gap-6">
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                <div className="justify-center text-black text-xs font-medium font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 h-2.5 relative">
                    <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                    <div className="w-40 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                  </div>
                  <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">71%</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                <div className="justify-center text-black text-xs font-medium font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 h-2.5 relative">
                    <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                    <div className="w-52 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                  </div>
                  <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">92%</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                <div className="justify-center text-black text-xs font-medium font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 h-2.5 relative">
                    <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                    <div className="w-14 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                  </div>
                  <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">32%</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                <div className="justify-center text-black text-xs font-medium font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 h-2.5 relative">
                    <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                    <div className="w-28 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                  </div>
                  <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">54%</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                <div className="justify-center text-black text-xs font-medium font-['Plus_Jakarta_Sans'] leading-none">Lorem ipsum </div>
                <div className="flex-1 flex justify-start items-center gap-2">
                  <div className="flex-1 h-2.5 relative">
                    <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                    <div className="w-48 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                  </div>
                  <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">86%</div>
                </div>
              </div>
              <div className="self-stretch inline-flex justify-start items-center gap-3">
                            <div className="flex-1 h-2.5 relative">
                                <div className="w-60 h-2.5 left-0 top-[0.50px] absolute bg-zinc-300 rounded-full" />
                                <div className="w-40 h-2.5 left-0 top-[0.50px] absolute bg-indigo-500 rounded-full" />
                            </div>
                            <div className="justify-center text-black text-[10px] font-semibold font-['Plus_Jakarta_Sans'] leading-3">71%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>